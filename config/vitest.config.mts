import path from 'path';
import tsconfigPaths from 'vite-tsconfig-paths';
import { defineConfig } from 'vitest/config';

export default defineConfig({
  plugins: [tsconfigPaths()],
  test: {
    name: 'unit-tests',
    environment: 'jsdom',
    globals: true,
    setupFiles: './config/vitest.setup.ts',
    include: ['test/**/*.test.{ts,tsx}'],
    exclude: ['**/*.stories.test.tsx'],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'lcov'],
      exclude: [
        '**/.next/**',
        '**/*.d.ts',
        '**/*.stories.tsx',
        '**/*.stories.test.tsx',
        'config/**',
        'vitest.*.ts',
        'next.config.ts',
        '.storybook/**',
      ],
    },
    watch: true,
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, '../src'),
      '@test': path.resolve(__dirname, '../test'),
    },
  },
});
