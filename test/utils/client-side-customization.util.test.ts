import type { Customization } from '@/contexts/customization';
import { resetCustomization, updateCustomization } from '@/utils/client-side-customization.util';
import Cookies from 'js-cookie';
import { beforeEach, describe, expect, it, vi } from 'vitest';

vi.mock('js-cookie', () => ({
  default: {
    set: vi.fn(),
    remove: vi.fn(),
  },
}));

// Mock reload
const mockReload = vi.fn();
Object.defineProperty(window, 'location', {
  value: { reload: mockReload },
  writable: true,
});

describe('client-side-customization.util', () => {
  const customization: Customization = {
    layout: 'vertical-shells-light',
    paletteMode: 'light',
    stretch: true,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('updateCustomization', () => {
    it('should set customization cookie and reload the page', () => {
      updateCustomization(customization);

      expect(Cookies.set).toHaveBeenCalledWith(
        'uifort.customization',
        JSON.stringify(customization)
      );
      expect(mockReload).toHaveBeenCalled();
    });

    it('should log error if updateCustomization throws', () => {
      (Cookies.set as any).mockImplementationOnce(() => {
        throw new Error('cookie error');
      });
      const spy = vi.spyOn(console, 'error').mockImplementation(() => {});

      updateCustomization(customization);

      expect(spy).toHaveBeenCalledWith(expect.any(Error));
      spy.mockRestore();
    });
  });

  describe('resetCustomization', () => {
    it('should remove customization cookie and reload the page', () => {
      resetCustomization();

      expect(Cookies.remove).toHaveBeenCalledWith('uifort.customization');
      expect(mockReload).toHaveBeenCalled();
    });

    it('should log error if resetCustomization throws', () => {
      (Cookies.remove as any).mockImplementationOnce(() => {
        throw new Error('remove error');
      });
      const spy = vi.spyOn(console, 'error').mockImplementation(() => {});

      resetCustomization();

      expect(spy).toHaveBeenCalledWith(expect.any(Error));
      spy.mockRestore();
    });
  });
});
