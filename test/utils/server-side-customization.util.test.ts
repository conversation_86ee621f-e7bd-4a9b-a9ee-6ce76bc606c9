import { restoreCustomization } from '@/utils/server-side-customization.util';
import { cookies } from 'next/headers';
import { beforeEach, describe, expect, it, vi } from 'vitest';

vi.mock('next/headers', () => ({
  cookies: vi.fn(),
}));

describe('server-side-customization.util', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should return undefined if no customization cookie exists', async () => {
    (cookies as any).mockResolvedValue({
      has: () => false,
    });

    const result = await restoreCustomization();
    expect(result).toBeUndefined();
  });

  it('should parse and return customization if cookie exists', async () => {
    const mockCustomization = { theme: 'light' };
    (cookies as any).mockResolvedValue({
      has: (key: string) => key === 'uifort.customization',
      get: () => ({ value: JSON.stringify(mockCustomization) }),
    });

    const result = await restoreCustomization();
    expect(result).toEqual(mockCustomization);
  });

  it('should return undefined and log error if parsing fails', async () => {
    (cookies as any).mockResolvedValue({
      has: () => true,
      get: () => ({ value: '{invalidJson}' }),
    });

    const spy = vi.spyOn(console, 'error').mockImplementation(() => {});
    const result = await restoreCustomization();

    expect(result).toBeUndefined();
    expect(spy).toHaveBeenCalledWith('Failed to restore customization', expect.any(Error));

    spy.mockRestore();
  });

  it('should return undefined if cookie value is undefined', async () => {
    (cookies as any).mockResolvedValue({
      has: () => true,
      get: () => undefined,
    });

    const result = await restoreCustomization();
    expect(result).toBeUndefined();
  });
});
