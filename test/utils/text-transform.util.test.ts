// transform-text.util.test.ts
import type { ChangeCaseTransform } from '@/components/base/typography/Typography.type';
import { transformText } from '@/utils/text-transform.util';
import { describe, expect, it } from 'vitest';

describe('transformText', () => {
  const cases: Array<[ChangeCaseTransform, string, string]> = [
    ['camelCase', 'hello world', 'helloWorld'],
    ['capitalCase', 'hello world', 'Hello World'],
    ['constantCase', 'hello world', 'HELLO_WORLD'],
    ['dotCase', 'hello world', 'hello.world'],
    ['kebabCase', 'hello world', 'hello-world'],
    ['lowercase', 'HeLLo WoRLd', 'hello world'],
    ['noCase', 'helloWorld', 'hello world'],
    ['none', 'Hello World', 'Hello World'],
    ['pascalCase', 'hello world', 'HelloWorld'],
    ['pascalSnakeCase', 'hello world', 'Hello_World'],
    ['pathCase', 'hello world', 'hello/world'],
    ['snakeCase', 'hello world', 'hello_world'],
    ['trainCase', 'hello world', 'Hello-World'],
    ['uppercase', 'hello world', 'HELLO WORLD'],
  ];

  it.each(cases)('should transform text using %s', (transform, input, expected) => {
    expect(transformText(input, transform)).toBe(expected);
  });

  describe('sentenceCase', () => {
    it('should capitalize the first letter of a sentence', () => {
      expect(transformText('hello world. how are you?', 'sentenceCase')).toBe(
        'Hello world. How are you?'
      );
    });

    it('should handle multiple punctuation types', () => {
      expect(transformText('wow! amazing? yes.', 'sentenceCase')).toBe('Wow! Amazing? Yes.');
    });

    it('should not remove punctuation', () => {
      expect(transformText('hello.world!next?', 'sentenceCase')).toBe('Hello.World!Next?');
    });
  });

  describe('edge cases', () => {
    it('should return empty string when input is empty', () => {
      expect(transformText('', 'camelCase')).toBe('');
      expect(transformText('', 'sentenceCase')).toBe('');
      expect(transformText('', 'none')).toBe('');
    });

    it('should return the same text for unknown transform', () => {
      expect(transformText('hello world', 'unknown' as ChangeCaseTransform)).toBe('hello world');
    });

    it('should not break on text with numbers and symbols', () => {
      expect(transformText('user 123_name!', 'snakeCase')).toBe('user_123_name');
      expect(transformText('user 123_name!', 'constantCase')).toBe('USER_123_NAME');
    });
  });
});
