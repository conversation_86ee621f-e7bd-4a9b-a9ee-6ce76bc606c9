import { convertRouteToRegex, isRouteActive } from '@/utils/route.util';
import { describe, expect, it } from 'vitest';

describe('Route Utility Functions', () => {
  describe('convertRouteToRegex', () => {
    it('should convert a simple route to regex', () => {
      const route = '/users';
      const regex = convertRouteToRegex(route);

      expect(regex).toBeInstanceOf(RegExp);
      expect(regex.test('/users')).toBe(true);
      expect(regex.test('/users/123')).toBe(false);
      expect(regex.test('/user')).toBe(false);
    });

    it('should escape special regex characters in routes', () => {
      const route = '/users?query=test&sort=name';
      const regex = convertRouteToRegex(route);

      expect(regex.test('/users?query=test&sort=name')).toBe(true);
      expect(regex.test('/usersXqueryXtestXsortXname')).toBe(false);
    });

    it('should handle routes with dynamic segments', () => {
      const route = '/users/[id]';
      const regex = convertRouteToRegex(route);

      expect(regex.test('/users/123')).toBe(true);
      expect(regex.test('/users/abc')).toBe(true);
      expect(regex.test('/users/user-123')).toBe(true);
      expect(regex.test('/users/')).toBe(false);
      expect(regex.test('/users/123/profile')).toBe(false);
    });

    it('should handle routes with multiple dynamic segments', () => {
      const route = '/users/[id]/posts/[postId]';
      const regex = convertRouteToRegex(route);

      expect(regex.test('/users/123/posts/456')).toBe(true);
      expect(regex.test('/users/abc/posts/def')).toBe(true);
      expect(regex.test('/users/123/posts')).toBe(false);
      expect(regex.test('/users/posts/456')).toBe(false);
    });

    it('should handle routes with dynamic segments containing special characters', () => {
      const route = '/api/[version]/users/[user-id]';
      const regex = convertRouteToRegex(route);

      expect(regex.test('/api/v1/users/user-123')).toBe(true);
      expect(regex.test('/api/v2.1/users/admin-456')).toBe(true);
      expect(regex.test('/api//users/123')).toBe(false);
    });

    it('should handle complex routes with mixed static and dynamic segments', () => {
      const route = '/dashboard/[tenant]/settings/[category]/edit';
      const regex = convertRouteToRegex(route);

      expect(regex.test('/dashboard/company1/settings/general/edit')).toBe(true);
      expect(regex.test('/dashboard/tenant-123/settings/security/edit')).toBe(true);
      expect(regex.test('/dashboard/company1/settings/general')).toBe(false);
      expect(regex.test('/dashboard/company1/settings/general/edit/extra')).toBe(false);
    });

    it('should handle root route', () => {
      const route = '/';
      const regex = convertRouteToRegex(route);

      expect(regex.test('/')).toBe(true);
      expect(regex.test('/home')).toBe(false);
      expect(regex.test('')).toBe(false);
    });

    it('should handle empty string route', () => {
      const route = '';
      const regex = convertRouteToRegex(route);

      expect(regex.test('')).toBe(true);
      expect(regex.test('/')).toBe(false);
      expect(regex.test('/anything')).toBe(false);
    });

    it('should handle routes with dots and other special characters', () => {
      const route = '/api/v1.0/files/[filename].json';
      const regex = convertRouteToRegex(route);

      expect(regex.test('/api/v1.0/files/data.json')).toBe(true);
      expect(regex.test('/api/v1.0/files/config-file.json')).toBe(true);
      expect(regex.test('/api/v1.0/files/.json')).toBe(false);
    });
  });

  describe('isRouteActive', () => {
    describe('exact route matching', () => {
      it('should return true for exact route match', () => {
        expect(isRouteActive('/users', '/users')).toBe(true);
        expect(isRouteActive('/dashboard', '/dashboard')).toBe(true);
        expect(isRouteActive('/', '/')).toBe(true);
      });

      it('should return false for non-matching routes', () => {
        expect(isRouteActive('/users', '/posts')).toBe(false);
        expect(isRouteActive('/dashboard', '/settings')).toBe(false);
        expect(isRouteActive('/users', '/user')).toBe(false);
      });
    });

    describe('parent route matching', () => {
      it('should return true when pathname is a child of the route', () => {
        expect(isRouteActive('/users', '/users/123')).toBe(true);
        expect(isRouteActive('/dashboard', '/dashboard/settings')).toBe(true);
        expect(isRouteActive('/api', '/api/v1/users')).toBe(true);
      });

      it('should return false for similar but non-parent routes', () => {
        expect(isRouteActive('/user', '/users')).toBe(false);
        expect(isRouteActive('/users', '/userspace')).toBe(false);
        expect(isRouteActive('/api', '/application')).toBe(false);
      });

      it('should handle nested parent routes correctly', () => {
        expect(isRouteActive('/dashboard/settings', '/dashboard/settings/profile')).toBe(true);
        expect(isRouteActive('/api/v1', '/api/v1/users/123')).toBe(true);
        expect(isRouteActive('/dashboard/settings', '/dashboard/users')).toBe(false);
      });
    });

    describe('dynamic route matching', () => {
      it('should return true for dynamic route matches', () => {
        expect(isRouteActive('/users/[id]', '/users/123')).toBe(true);
        expect(isRouteActive('/posts/[slug]', '/posts/my-first-post')).toBe(true);
        expect(isRouteActive('/api/[version]', '/api/v1')).toBe(true);
      });

      it('should return false for non-matching dynamic routes', () => {
        expect(isRouteActive('/users/[id]', '/users')).toBe(false);
        expect(isRouteActive('/users/[id]', '/users/123/profile')).toBe(false);
        expect(isRouteActive('/posts/[slug]', '/posts/')).toBe(false);
      });

      it('should handle multiple dynamic segments', () => {
        expect(isRouteActive('/users/[id]/posts/[postId]', '/users/123/posts/456')).toBe(true);
        expect(
          isRouteActive('/[tenant]/dashboard/[section]', '/company1/dashboard/analytics')
        ).toBe(true);
        expect(isRouteActive('/users/[id]/posts/[postId]', '/users/123/posts')).toBe(false);
      });

      it('should combine dynamic matching with parent route logic', () => {
        expect(isRouteActive('/users/[id]', '/users/123/profile')).toBe(false);
        expect(isRouteActive('/posts/[slug]', '/posts/my-post/comments')).toBe(false);
        expect(isRouteActive('/api/[version]', '/api/v1/users')).toBe(false);
      });
    });

    describe('edge cases', () => {
      it('should return false when route is undefined', () => {
        expect(isRouteActive(undefined, '/users')).toBe(false);
        expect(isRouteActive(undefined, '/')).toBe(false);
        expect(isRouteActive(undefined, '')).toBe(false);
      });

      it('should handle empty pathname', () => {
        expect(isRouteActive('/', '')).toBe(false);
        expect(isRouteActive('', '')).toBe(false);
        expect(isRouteActive('/users', '')).toBe(false);
      });

      it('should handle root route correctly', () => {
        expect(isRouteActive('/', '/')).toBe(true);
        expect(isRouteActive('/', '/home')).toBe(false);
        expect(isRouteActive('/', '/users/123')).toBe(false);
      });

      it('should be case sensitive', () => {
        expect(isRouteActive('/Users', '/users')).toBe(false);
        expect(isRouteActive('/api/V1', '/api/v1')).toBe(false);
        expect(isRouteActive('/Dashboard', '/dashboard/settings')).toBe(false);
      });

      it('should handle routes with query parameters and fragments', () => {
        expect(isRouteActive('/users', '/users?page=1')).toBe(false);
        expect(isRouteActive('/users', '/users#section')).toBe(false);
        expect(isRouteActive('/users?page=1', '/users?page=1')).toBe(true);
      });
    });

    describe('complex scenarios', () => {
      it('should handle deeply nested dynamic routes', () => {
        const route = '/[tenant]/dashboard/[section]/[subsection]/[id]';
        expect(isRouteActive(route, '/company1/dashboard/users/admins/123')).toBe(true);
        expect(isRouteActive(route, '/company1/dashboard/users/admins/123/edit')).toBe(false);
        expect(isRouteActive(route, '/company1/dashboard/users/admins')).toBe(false);
      });

      it('should prioritize exact matches over regex matches', () => {
        // When pathname matches exactly, it should return true regardless of regex
        expect(isRouteActive('/users/[id]', '/users/[id]')).toBe(true);
      });

      it('should handle routes with special characters in dynamic segments', () => {
        expect(isRouteActive('/files/[filename]', '/files/document-v1.2.pdf')).toBe(true);
        expect(isRouteActive('/users/[email]', '/users/<EMAIL>')).toBe(true);
        expect(isRouteActive('/search/[query]', '/search/hello%20world')).toBe(true);
      });
    });
  });
});
