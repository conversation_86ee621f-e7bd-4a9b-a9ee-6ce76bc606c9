import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

// Helper to clear the module cache and reload the util fresh each test
const loadConfig = async () => {
  vi.resetModules();
  return await import('@/utils/config.util'); // adjust filename if needed
};

describe('config util', () => {
  const OLD_ENV = process.env;

  beforeEach(() => {
    vi.resetModules(); // ensure a fresh module load
    process.env = { ...OLD_ENV }; // copy original env
  });

  afterEach(() => {
    process.env = OLD_ENV; // restore after test
  });

  it('should return GTM id and MUI licenseKey when both env vars are set', async () => {
    process.env.NEXT_PUBLIC_GOOGLE_TAG_MANAGER_ID = 'GTM-12345';
    process.env.NEXT_PUBLIC_MUI_X_LICENSE_KEY = 'mui-license-abc';

    const { config } = await loadConfig();

    expect(config).toEqual({
      gtm: { id: 'GTM-12345' },
      mui: { licenseKey: 'mui-license-abc' },
    });
  });

  it('should set GTM id as undefined if env var is not provided', async () => {
    delete process.env.NEXT_PUBLIC_GOOGLE_TAG_MANAGER_ID;
    process.env.NEXT_PUBLIC_MUI_X_LICENSE_KEY = 'mui-license-xyz';

    const { config } = await loadConfig();

    expect(config.gtm.id).toBeUndefined();
    expect(config.mui.licenseKey).toBe('mui-license-xyz');
  });

  it('should default MUI licenseKey to empty string if env var is missing', async () => {
    process.env.NEXT_PUBLIC_GOOGLE_TAG_MANAGER_ID = 'GTM-67890';
    delete process.env.NEXT_PUBLIC_MUI_X_LICENSE_KEY;

    const { config } = await loadConfig();

    expect(config.gtm.id).toBe('GTM-67890');
    expect(config.mui.licenseKey).toBe(''); // because of `?? ''`
  });
});
