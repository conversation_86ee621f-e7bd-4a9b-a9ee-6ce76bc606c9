import NotFoundPage from '@/app/not-found';
import ROUTES from '@/router/routes';
import { render, screen } from '@testing-library/react';
import { useTranslation } from 'react-i18next';
import { beforeEach, describe, expect, it, vi, type Mock } from 'vitest';

// Mock react-i18next module
vi.mock('react-i18next', () => ({
  useTranslation: vi.fn(),
}));

describe('NotFoundPage', () => {
  const mockT = vi.fn();

  beforeEach(() => {
    vi.resetAllMocks();

    mockT.mockImplementation((key) => {
      const translations: Record<string, string> = {
        'common.label.pageNotFound': 'Page Not Found',
        'common.sentence.pageMoved': 'The page you are looking for might have been moved',
        'common.action.goToHome': 'Go to Home',
      };
      return translations[key] ?? key;
    });

    (useTranslation as Mock).mockReturnValue({ t: mockT });
  });

  it('should render the complete 404 page layout with all components when mounted', () => {
    render(<NotFoundPage />);

    // Check that WarningAvatar is rendered
    expect(screen.getByTestId('warning-avatar')).toBeInTheDocument();

    // Check that main heading is rendered with correct text
    expect(screen.getByRole('heading', { level: 2 })).toBeInTheDocument();
    expect(screen.getByText('Page Not Found')).toBeInTheDocument();

    // Check that subtitle is rendered with correct text
    expect(screen.getByRole('heading', { level: 4 })).toBeInTheDocument();
    expect(
      screen.getByText('The page you are looking for might have been moved')
    ).toBeInTheDocument();

    // Check that divider with styled box is rendered
    expect(screen.getByRole('separator')).toBeInTheDocument();

    // Check that the "Go to Home" button is rendered with correct properties
    const homeButton = screen.getByTestId('home-button');
    expect(homeButton).toBeInTheDocument();
    expect(homeButton).toHaveAttribute('href', ROUTES.INDEX);

    // Check that the container is present (instead of main role)
    expect(screen.getByTestId('warning-avatar').closest('.MuiContainer-root')).toBeInTheDocument();

    // Verify translation function was called with expected keys
    expect(mockT).toHaveBeenCalledWith('common.label.pageNotFound');
    expect(mockT).toHaveBeenCalledWith('common.sentence.pageMoved');
    expect(mockT).toHaveBeenCalledWith('common.action.goToHome');
  });

  it('should navigate to home page when the go to home button is clicked', async () => {
    render(<NotFoundPage />);

    const homeButton = screen.getByTestId('home-button');
    expect(homeButton).toHaveAttribute('href', ROUTES.INDEX);
    expect(homeButton.closest('a')).toHaveAttribute('href', ROUTES.INDEX);
  });
});
