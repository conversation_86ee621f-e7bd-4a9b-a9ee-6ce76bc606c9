import DashboardPage from '@/app/[accessId]/dashboard/page';
import type { AvatarIconProps } from '@/components/base/data-display/avatar';
import { useCustomization } from '@/hooks/ui/use-customization.hook';
import { usePageTitle } from '@/hooks/ui/use-page-title.hook';
import { render, screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';

// Mock all the dependencies
vi.mock('@/components/base/data-display/avatar', () => ({
  AvatarIcon: ({ icon, state, size, variant }: AvatarIconProps) => (
    <div
      data-testid="avatar-icon"
      data-state={state}
      data-size={size}
      data-variant={variant}
    >
      {icon}
    </div>
  ),
}));

vi.mock('@/components/base/headings/page-heading', () => ({
  PageHeading: ({ iconBox, title }: { iconBox: React.ReactNode; title: string }) => (
    <div data-testid="page-heading">
      <div data-testid="page-heading-icon">{iconBox}</div>
      <div data-testid="page-heading-title">{title}</div>
    </div>
  ),
}));

vi.mock('@/hooks/ui/use-customization.hook', () => ({
  useCustomization: vi.fn(),
}));

vi.mock('@/hooks/ui/use-page-title.hook', () => ({
  usePageTitle: vi.fn(),
}));

vi.mock('@mui/icons-material/DashboardTwoTone', () => ({
  default: () => <div data-testid="dashboard-icon">Dashboard Icon</div>,
}));

vi.mock('@mui/material', () => ({
  Box: ({ children, pb }: { children: React.ReactNode; pb: Record<string, number> }) => (
    <div
      data-testid="mui-box"
      data-pb={JSON.stringify(pb)}
    >
      {children}
    </div>
  ),
  Container: ({ children, maxWidth }: { children: React.ReactNode; maxWidth: string }) => (
    <div
      data-testid="mui-container"
      data-max-width={JSON.stringify(maxWidth)}
    >
      {children}
    </div>
  ),
}));

vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => {
      const translations: Record<string, string> = {
        'common.label.dashboard': 'Dashboard',
      };
      return translations[key] ?? key;
    },
  }),
}));

const mockUseCustomization = vi.mocked(useCustomization);
const mockUsePageTitle = vi.mocked(usePageTitle);

describe('DashboardPage', () => {
  beforeEach(() => {
    vi.clearAllMocks();

    // Default mock implementations
    mockUseCustomization.mockReturnValue({
      stretch: false,
      colorPreset: 'ultraViolet',
      direction: 'ltr',
      layout: 'vertical-shells-light',
      paletteMode: 'light',
      handleReset: vi.fn(),
      handleUpdate: vi.fn(),
      isCustom: false,
      isInitialized: true,
    });
  });

  it('should render the dashboard page with all components', () => {
    render(<DashboardPage />);

    expect(screen.getByTestId('page-heading')).toBeInTheDocument();
    expect(screen.getByTestId('page-heading-title')).toHaveTextContent('Dashboard');
    expect(screen.getByTestId('avatar-icon')).toBeInTheDocument();
    expect(screen.getByTestId('dashboard-icon')).toBeInTheDocument();
    expect(screen.getByTestId('mui-box')).toBeInTheDocument();
    expect(screen.getByTestId('mui-container')).toBeInTheDocument();
  });

  it('should call usePageTitle with correct title', () => {
    render(<DashboardPage />);

    expect(mockUsePageTitle).toHaveBeenCalledWith('common.label.dashboard');
    expect(mockUsePageTitle).toHaveBeenCalledTimes(1);
  });

  it('should render AvatarIcon with correct props', () => {
    render(<DashboardPage />);

    const avatarIcon = screen.getByTestId('avatar-icon');
    expect(avatarIcon).toHaveAttribute('data-state', 'honeyGold');
    expect(avatarIcon).toHaveAttribute('data-size', '44');
    expect(avatarIcon).toHaveAttribute('data-variant', 'rounded');
    expect(avatarIcon).toContainElement(screen.getByTestId('dashboard-icon'));
  });

  it('should render Container with maxWidth=xl when stretch is false', () => {
    mockUseCustomization.mockReturnValue({
      stretch: false,
      colorPreset: 'ultraViolet',
      direction: 'ltr',
      layout: 'vertical-shells-light',
      paletteMode: 'light',
      handleReset: vi.fn(),
      handleUpdate: vi.fn(),
      isCustom: false,
      isInitialized: true,
    });

    render(<DashboardPage />);

    const container = screen.getByTestId('mui-container');
    expect(container).toHaveAttribute('data-max-width', '"xl"');
  });

  it('should render Container with maxWidth=false when stretch is true', () => {
    mockUseCustomization.mockReturnValue({
      stretch: true,
      colorPreset: 'ultraViolet',
      direction: 'ltr',
      layout: 'vertical-shells-light',
      paletteMode: 'light',
      handleReset: vi.fn(),
      handleUpdate: vi.fn(),
      isCustom: false,
      isInitialized: true,
    });

    render(<DashboardPage />);

    const container = screen.getByTestId('mui-container');
    expect(container).toHaveAttribute('data-max-width', 'false');
  });

  it('should render Box with correct padding props', () => {
    render(<DashboardPage />);

    const box = screen.getByTestId('mui-box');
    expect(box).toHaveAttribute('data-pb', '{"xs":2,"sm":3}');
  });

  it('should render PageHeading when component renders', () => {
    render(<DashboardPage />);

    expect(screen.getByTestId('page-heading')).toBeInTheDocument();
  });

  it('should call all required hooks', () => {
    render(<DashboardPage />);

    expect(mockUsePageTitle).toHaveBeenCalled();
    expect(mockUseCustomization).toHaveBeenCalled();
  });

  it('should render the component structure correctly', () => {
    render(<DashboardPage />);

    // Check the overall structure
    const pageHeading = screen.getByTestId('page-heading');
    const box = screen.getByTestId('mui-box');
    const container = screen.getByTestId('mui-container');

    expect(pageHeading).toBeInTheDocument();
    expect(box).toBeInTheDocument();
    expect(container).toBeInTheDocument();

    // Check that container is inside box
    expect(box).toContainElement(container);
  });

  it('should use customization hook to determine container maxWidth', () => {
    render(<DashboardPage />);

    expect(mockUseCustomization).toHaveBeenCalled();

    // Verify the hook is used for determining container behavior
    const container = screen.getByTestId('mui-container');
    expect(container).toBeInTheDocument();
  });

  it('should render dashboard icon inside avatar component', () => {
    render(<DashboardPage />);

    const avatarIcon = screen.getByTestId('avatar-icon');
    const dashboardIcon = screen.getByTestId('dashboard-icon');

    expect(avatarIcon).toContainElement(dashboardIcon);
  });

  it('should translate dashboard title correctly', () => {
    render(<DashboardPage />);

    expect(screen.getByTestId('page-heading-title')).toHaveTextContent('Dashboard');
  });
});
