import Layout from '@/app/[accessId]/layout';
import { render, screen } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';

// Mock the Layout component from @/layouts
vi.mock('@/layouts', () => ({
  Layout: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="mocked-layout">
      <div data-testid="layout-children">{children}</div>
    </div>
  ),
}));

describe('[accessId]/layout', () => {
  it('should render the Layout component from @/layouts', () => {
    const testChildren = <div data-testid="test-content">Test Content</div>;

    render(<Layout>{testChildren}</Layout>);

    expect(screen.getByTestId('mocked-layout')).toBeInTheDocument();
    expect(screen.getByTestId('layout-children')).toBeInTheDocument();
    expect(screen.getByTestId('test-content')).toBeInTheDocument();
  });

  it('should pass children to the Layout component', () => {
    const complexChildren = (
      <div>
        <header data-testid="header">Header</header>
        <main data-testid="main">Main Content</main>
        <footer data-testid="footer">Footer</footer>
      </div>
    );

    render(<Layout>{complexChildren}</Layout>);

    expect(screen.getByTestId('header')).toBeInTheDocument();
    expect(screen.getByTestId('main')).toBeInTheDocument();
    expect(screen.getByTestId('footer')).toBeInTheDocument();
  });

  it('should handle empty children', () => {
    render(<Layout>{null}</Layout>);

    expect(screen.getByTestId('mocked-layout')).toBeInTheDocument();
    expect(screen.getByTestId('layout-children')).toBeInTheDocument();
  });

  it('should handle multiple children', () => {
    render(
      <Layout>
        <div data-testid="child-1">Child 1</div>
        <div data-testid="child-2">Child 2</div>
        <div data-testid="child-3">Child 3</div>
      </Layout>
    );

    expect(screen.getByTestId('child-1')).toBeInTheDocument();
    expect(screen.getByTestId('child-2')).toBeInTheDocument();
    expect(screen.getByTestId('child-3')).toBeInTheDocument();
  });

  it('should be a client component (use client directive)', () => {
    // This test verifies the 'use client' directive is present
    // We can't directly test the directive, but we can test that the component
    // behaves as a client component by rendering it without issues
    const testContent = <div>Client component test</div>;

    expect(() => render(<Layout>{testContent}</Layout>)).not.toThrow();
    expect(screen.getByText('Client component test')).toBeInTheDocument();
  });
});
