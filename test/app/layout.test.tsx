import Layout from '@/app/layout';
import type { Customization } from '@/contexts/customization';
import { restoreCustomization } from '@/utils/server-side-customization.util';
import { render, screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';

// Mock all the dependencies
vi.mock('@/components/base/feedback/nprogress', () => ({
  NProgress: () => <div data-testid="nprogress">NProgress</div>,
}));

vi.mock('@/contexts/app', () => ({
  AppStoreProvider: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="app-store-provider">{children}</div>
  ),
}));

vi.mock('@/layouts/document', () => ({
  DocumentLayout: ({
    children,
    customization,
  }: {
    children: React.ReactNode;
    customization: Customization;
  }) => (
    <div
      data-testid="document-layout"
      data-customization={customization !== undefined ? JSON.stringify(customization) : 'undefined'}
    >
      {children}
    </div>
  ),
}));

vi.mock('@/providers/tanstack/query/Provider', () => ({
  Provider: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="query-provider">{children}</div>
  ),
}));

vi.mock('@/utils/server-side-customization.util', () => ({
  restoreCustomization: vi.fn(),
}));

vi.mock('next-navigation-guard', () => ({
  NavigationGuardProvider: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="navigation-guard-provider">{children}</div>
  ),
}));

// Mock global CSS import
vi.mock('@/global.css', () => ({}));

// Type the mocked function
const mockRestoreCustomization = vi.mocked(restoreCustomization);

describe('Layout Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the basic HTML structure correctly', async () => {
    const mockCustomization = {
      colorPreset: 'ultraViolet' as const,
      direction: 'ltr' as const,
      layout: 'vertical-shells-light' as const,
      paletteMode: 'dark' as const,
      stretch: false,
    };
    mockRestoreCustomization.mockResolvedValue(mockCustomization);

    const LayoutComponent = await Layout({ children: <div>Test Content</div> });
    render(LayoutComponent);

    // Check for HTML structure
    expect(document.documentElement).toHaveAttribute('lang', 'en');
    expect(document.body).toBeInTheDocument();
  });

  it('renders all provider components in correct order', async () => {
    const mockCustomization = {
      colorPreset: 'ultraViolet' as const,
      paletteMode: 'light' as const,
    };
    mockRestoreCustomization.mockResolvedValue(mockCustomization);

    const LayoutComponent = await Layout({ children: <div>Test Content</div> });
    render(LayoutComponent);

    // Check provider hierarchy
    const navigationGuard = screen.getByTestId('navigation-guard-provider');
    const appStore = screen.getByTestId('app-store-provider');
    const queryProvider = screen.getByTestId('query-provider');
    const documentLayout = screen.getByTestId('document-layout');

    expect(navigationGuard).toBeInTheDocument();
    expect(appStore).toBeInTheDocument();
    expect(queryProvider).toBeInTheDocument();
    expect(documentLayout).toBeInTheDocument();

    // Verify nesting order
    expect(navigationGuard).toContainElement(appStore);
    expect(appStore).toContainElement(queryProvider);
    expect(queryProvider).toContainElement(documentLayout);
  });

  it('passes customization to DocumentLayout', async () => {
    const mockCustomization = {
      colorPreset: 'ultraViolet' as const,
      direction: 'ltr' as const,
      layout: 'vertical-shells-dark' as const,
      paletteMode: 'dark' as const,
      stretch: true,
    };
    mockRestoreCustomization.mockResolvedValue(mockCustomization);

    const LayoutComponent = await Layout({ children: <div>Test Content</div> });
    render(LayoutComponent);

    const documentLayout = screen.getByTestId('document-layout');
    expect(documentLayout).toHaveAttribute('data-customization', JSON.stringify(mockCustomization));
  });

  it('renders NProgress component', async () => {
    const mockCustomization = {};
    mockRestoreCustomization.mockResolvedValue(mockCustomization);

    const LayoutComponent = await Layout({ children: <div>Test Content</div> });
    render(LayoutComponent);

    expect(screen.getByTestId('nprogress')).toBeInTheDocument();
  });

  it('renders children content', async () => {
    const mockCustomization = {};
    mockRestoreCustomization.mockResolvedValue(mockCustomization);

    const testContent = <div data-testid="test-children">Child Content</div>;
    const LayoutComponent = await Layout({ children: testContent });
    render(LayoutComponent);

    expect(screen.getByTestId('test-children')).toBeInTheDocument();
    expect(screen.getByText('Child Content')).toBeInTheDocument();
  });

  it('calls restoreCustomization function', async () => {
    const mockCustomization = { paletteMode: 'light' as const };
    mockRestoreCustomization.mockResolvedValue(mockCustomization);

    await Layout({ children: <div>Test</div> });

    expect(mockRestoreCustomization).toHaveBeenCalledTimes(1);
    expect(mockRestoreCustomization).toHaveBeenCalledWith();
  });

  it('handles empty customization object', async () => {
    mockRestoreCustomization.mockResolvedValue({});

    const LayoutComponent = await Layout({ children: <div>Test</div> });
    render(LayoutComponent);

    const documentLayout = screen.getByTestId('document-layout');
    expect(documentLayout).toHaveAttribute('data-customization', '{}');
  });

  it('handles undefined customization', async () => {
    mockRestoreCustomization.mockResolvedValue(undefined);

    const LayoutComponent = await Layout({ children: <div>Test</div> });
    render(LayoutComponent);

    const documentLayout = screen.getByTestId('document-layout');
    expect(documentLayout).toHaveAttribute('data-customization', 'undefined');
  });

  it('renders with complex children structure', async () => {
    const mockCustomization = {};
    mockRestoreCustomization.mockResolvedValue(mockCustomization);

    const complexChildren = (
      <div>
        <header>Header</header>
        <main>
          <section>Section 1</section>
          <section>Section 2</section>
        </main>
        <footer>Footer</footer>
      </div>
    );

    const LayoutComponent = await Layout({ children: complexChildren });
    render(LayoutComponent);

    expect(screen.getByText('Header')).toBeInTheDocument();
    expect(screen.getByText('Section 1')).toBeInTheDocument();
    expect(screen.getByText('Section 2')).toBeInTheDocument();
    expect(screen.getByText('Footer')).toBeInTheDocument();
  });
});
