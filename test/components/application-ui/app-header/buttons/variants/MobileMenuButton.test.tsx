import MobileMenuButton from '@/components/application-ui/app-header/buttons/variants/MobileMenuButton';
import { useTheme } from '@mui/material/styles';
import { render, screen } from '@testing-library/react';
import { useTranslation } from 'react-i18next';
import { vi } from 'vitest';

vi.mock('@mui/material/styles', async () => {
  const actual = await vi.importActual('@mui/material/styles');
  return {
    ...actual,
    useTheme: vi.fn(),
  };
});

vi.mock('react-i18next', async () => {
  const actual = await vi.importActual('react-i18next');
  return {
    ...actual,
    useTranslation: vi.fn(),
  };
});

describe('MobileMenuButton', () => {
  it('should render the MobileMenuButton component without errors', () => {
    const mockOnClick = vi.fn();
    const mockTheme = { palette: { primary: { main: '#000' } } };
    const mockT = vi.fn((key) => key);

    vi.mocked(useTheme).mockReturnValue(mockTheme as any);
    vi.mocked(useTranslation).mockReturnValue({ t: mockT } as any);

    render(<MobileMenuButton onClick={mockOnClick} />);

    expect(screen.getByRole('button')).toBeInTheDocument();
    expect(mockT).toHaveBeenCalledWith('common.label.toggleMenu');
  });

  it('should display MenuRoundedIcon within the button', () => {
    const mockOnClick = vi.fn();
    const mockTheme = { palette: { primary: { main: '#000' } } };
    const mockT = vi.fn((key) => key);

    vi.mocked(useTheme).mockReturnValue(mockTheme as any);
    vi.mocked(useTranslation).mockReturnValue({ t: mockT } as any);

    render(<MobileMenuButton onClick={mockOnClick} />);

    const button = screen.getByRole('button');
    const icon = button.querySelector('[data-testid="MenuRoundedIcon"]');

    expect(icon).toBeInTheDocument();
  });

  it('should apply common button styles from getCommonButtonStyles with theme', () => {
    const mockOnClick = vi.fn();
    const mockTheme = { palette: { primary: { main: '#000' } } };
    const mockT = vi.fn((key) => key);
    const mockStyles = { padding: '10px', margin: '5px' };

    vi.mocked(useTheme).mockReturnValue(mockTheme as any);
    vi.mocked(useTranslation).mockReturnValue({ t: mockT } as any);

    const getCommonButtonStylesMock = vi.fn().mockReturnValue(mockStyles);
    vi.doMock('../Buttons.style', () => ({
      getCommonButtonStyles: getCommonButtonStylesMock,
    }));

    render(<MobileMenuButton onClick={mockOnClick} />);

    const button = screen.getByRole('button');

    expect(button).toBeInTheDocument();
  });

  it('should call onClick handler when button is clicked', () => {
    const mockOnClick = vi.fn();
    const mockTheme = { palette: { primary: { main: '#000' } } };
    const mockT = vi.fn((key) => key);

    vi.mocked(useTheme).mockReturnValue(mockTheme as any);
    vi.mocked(useTranslation).mockReturnValue({ t: mockT } as any);

    render(<MobileMenuButton onClick={mockOnClick} />);

    const button = screen.getByRole('button');
    button.click();

    expect(mockOnClick).toHaveBeenCalledTimes(1);
  });

  it('should display translated label from common.label.toggleMenu as button title', () => {
    const mockOnClick = vi.fn();
    const mockTheme = { palette: { primary: { main: '#000' } } };
    const mockT = vi.fn((key) => {
      if (key === 'common.label.toggleMenu') {
        return 'Toggle Menu';
      }
      return key;
    });
  
    vi.mocked(useTheme).mockReturnValue(mockTheme as any);
    vi.mocked(useTranslation).mockReturnValue({ t: mockT } as any);
  
    render(<MobileMenuButton onClick={mockOnClick} />);
  
    const button = screen.getByRole('button');
  
    expect(mockT).toHaveBeenCalledWith('common.label.toggleMenu');
    expect(button).toHaveAccessibleName('Toggle Menu');
  });
});
