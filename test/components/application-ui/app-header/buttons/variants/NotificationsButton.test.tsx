import NotificationsButton from '@/components/application-ui/app-header/buttons/variants/NotificationsButton';
import { transformText } from '@/utils/text-transform.util';
import { useTheme } from '@mui/material/styles';
import { render, screen } from '@testing-library/react';
import { useTranslation } from 'react-i18next';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

vi.mock('@mui/material/styles', async () => {
  const actual = await vi.importActual('@mui/material/styles');
  return {
    ...actual,
    useTheme: vi.fn(),
  };
});

vi.mock('react-i18next', async () => {
  const actual = await vi.importActual('react-i18next');
  return {
    ...actual,
    useTranslation: vi.fn(),
  };
});

vi.mock('@/utils/text-transform.util', () => ({
  transformText: vi.fn(),
}));

describe('NotificationsButton', () => {
  const mockOnClick = vi.fn();
  const mockT = vi.fn((key: string) => key) as any;
  const mockTheme = {
    palette: {
      primary: { main: '#000' },
    },
    spacing: (val: number) => val * 8,
  };

  beforeEach(() => {
    vi.mocked(useTranslation).mockReturnValue({
      t: mockT,
      i18n: {} as any,
      ready: true,
    } as any);
    vi.mocked(useTheme).mockReturnValue(mockTheme as any);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('should render the NotificationsButton component', () => {
    vi.mocked(transformText).mockReturnValue('Notifications');

    render(<NotificationsButton onClick={mockOnClick} />);

    const button = screen.getByRole('button');
    expect(button).toBeInTheDocument();
  });

  it('should call translation function with correct key', () => {
    vi.mocked(transformText).mockReturnValue('Notifications');

    render(<NotificationsButton onClick={mockOnClick} />);

    expect(mockT).toHaveBeenCalledWith('common.label.notifications');
  });

  it('should transform text to sentence case', () => {
    vi.mocked(transformText).mockReturnValue('Notifications');

    render(<NotificationsButton onClick={mockOnClick} />);

    expect(transformText).toHaveBeenCalledWith('common.label.notifications', 'sentenceCase');
  });

  it('should set correct title attribute on button', () => {
    const transformedText = 'Notifications';
    vi.mocked(transformText).mockReturnValue(transformedText);

    render(<NotificationsButton onClick={mockOnClick} />);

    const button = screen.getByRole('button');
    expect(button).toHaveAccessibleName(transformedText);
  });

  it('should call onClick handler when button is clicked', () => {
    vi.mocked(transformText).mockReturnValue('Notifications');

    render(<NotificationsButton onClick={mockOnClick} />);

    const button = screen.getByRole('button');
    button.click();

    expect(mockOnClick).toHaveBeenCalledTimes(1);
  });

  it('should render with NotificationsNone icon', () => {
    vi.mocked(transformText).mockReturnValue('Notifications');

    render(<NotificationsButton onClick={mockOnClick} />);

    const button = screen.getByRole('button');
    const icon = button.querySelector('svg');

    expect(icon).toBeInTheDocument();
  });
});
