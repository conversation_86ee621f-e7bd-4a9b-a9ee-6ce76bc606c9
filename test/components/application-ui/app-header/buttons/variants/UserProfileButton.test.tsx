import { UserProfileButton } from '@/components/application-ui/app-header/buttons/variants/UserProfileButton';
import { useAuth } from '@/hooks/auth/use-auth.hook';
import { createTheme } from '@mui/material';
import { ThemeProvider } from '@mui/material/styles';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { useTranslation } from 'react-i18next';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

vi.mock('@/hooks/auth/use-auth.hook');
vi.mock('react-i18next', async () => {
  const actual = await vi.importActual('react-i18next');
  return {
    ...actual,
    useTranslation: vi.fn(),
  };
});
vi.mock('@/utils/text-transform.util', () => ({
  transformText: vi.fn((text) => text),
}));
vi.mock('@/components/base/data-display/avatar', () => ({
  stringAvatar: vi.fn(() => ({
    sx: { bgcolor: '#000' },
    children: 'U',
  })),
}));

describe('UserProfileButton', () => {
  const mockTheme = createTheme();
  const mockPopover = {
    open: false,
    anchorRef: { current: null },
    handleOpen: vi.fn(),
    handleClose: vi.fn(),
  };
  const mockT = vi.fn((key) => key);

  const mockUser = {
    username: 'testuser',
    avatar: 'https://example.com/avatar.jpg',
  };

  beforeEach(() => {
    vi.mocked(useAuth).mockReturnValue({
      user: mockUser,
    } as any);

    vi.mocked(useTranslation).mockReturnValue({ t: mockT } as any);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  const renderComponent = (props = {}) => {
    return render(
      <ThemeProvider theme={mockTheme}>
        <UserProfileButton popover={{ ...mockPopover, handleToggle: vi.fn(), ...props }} />
      </ThemeProvider>
    );
  };

  describe('Rendering', () => {
    it('should render the button with user avatar', () => {
      renderComponent();

      const button = screen.getByRole('button', { name: /common.label.userProfile/i });
      expect(button).toBeInTheDocument();
      expect(button).toHaveAttribute('id', 'profile-button');
    });

    it('should render avatar with user username as alt text', () => {
      renderComponent();

      const avatar = screen.getByAltText(mockUser.username);
      expect(avatar).toBeInTheDocument();
    });

    it('should render avatar with user avatar src', () => {
      renderComponent();

      const avatar = screen.getByAltText(mockUser.username);
      expect(avatar).toBeInTheDocument();
      expect(avatar).toHaveAttribute('src', expect.stringContaining(mockUser.avatar));
    });
  });

  describe('Accessibility', () => {
    it('should have correct aria attributes when popover is closed', () => {
      renderComponent();

      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('aria-haspopup', 'true');
      expect(button).not.toHaveAttribute('aria-expanded', 'true');
      expect(button).not.toHaveAttribute('aria-controls');
    });

    it('should have correct aria attributes when popover is open', () => {
      renderComponent({ open: true });

      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('aria-haspopup', 'true');
      expect(button).toHaveAttribute('aria-expanded', 'true');
      expect(button).toHaveAttribute('aria-controls', 'profile-menu');
    });

    it('should have title attribute for tooltip', () => {
      renderComponent();

      const button = screen.getByRole('button');
      expect(button).toHaveAccessibleName('common.label.userProfile');
    });
  });

  describe('Interactions', () => {
    it('should call handleOpen when button is clicked', async () => {
      const user = userEvent.setup();
      const handleOpen = vi.fn();

      renderComponent({ handleOpen });

      const button = screen.getByRole('button');
      await user.click(button);

      expect(handleOpen).toHaveBeenCalledTimes(1);
    });

    it('should not call handleOpen when button is disabled', async () => {
      const user = userEvent.setup();
      const handleOpen = vi.fn();

      const { container } = renderComponent({ handleOpen });
      const button = container.querySelector('button[disabled]');

      if (button) {
        await user.click(button);
        expect(handleOpen).not.toHaveBeenCalled();
      }
    });
  });

  describe('Styling', () => {
    it('should apply custom icon style', () => {
      renderComponent();

      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
    });

    it('should apply inherit color', () => {
      renderComponent();

      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
    });
  });

  describe('Integration with hooks', () => {
    it('should use translation hook for button title', () => {
      const mockT = vi.fn((key) => `translated_${key}`);
      vi.mocked(useTranslation).mockReturnValue({ t: mockT } as any);

      renderComponent();

      expect(mockT).toHaveBeenCalledWith('common.label.userProfile');
    });

    it('should use auth hook to get user data', () => {
      renderComponent();

      expect(useAuth).toHaveBeenCalled();
    });
  });

  describe('Avatar configuration', () => {
    it('should pass correct props to stringAvatar', async () => {
      const stringAvatarModule = await import('@/components/base/data-display/avatar');
      const { stringAvatar } = stringAvatarModule;

      renderComponent();

      expect(stringAvatar).toHaveBeenCalledWith(
        mockUser.username,
        expect.objectContaining({
          palette: expect.any(Object),
          typography: expect.any(Object),
        }),
        {
          borderRadius: 'inherit',
          height: 36,
          width: 36,
        }
      );
    });
  });
});
