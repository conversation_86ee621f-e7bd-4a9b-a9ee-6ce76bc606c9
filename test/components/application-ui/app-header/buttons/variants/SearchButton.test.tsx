import SearchButton from '@/components/application-ui/app-header/buttons/variants/SearchButton';
import { useTheme } from '@mui/material/styles';
import { render, screen } from '@testing-library/react';
import { useTranslation } from 'react-i18next';
import { vi } from 'vitest';

vi.mock('@mui/material/styles', async () => {
  const actual = await vi.importActual('@mui/material/styles');
  return {
    ...actual,
    useTheme: vi.fn(),
  };
});

vi.mock('react-i18next', async () => {
  const actual = await vi.importActual('react-i18next');
  return {
    ...actual,
    useTranslation: vi.fn(),
  };
});

describe('SearchButton', () => {
  it('should render the SearchButton component without errors', () => {
    const mockOnClick = vi.fn();
    const mockTheme = { palette: { primary: { main: '#000' } } };
    const mockT = vi.fn((key) => key);

    vi.mocked(useTheme).mockReturnValue(mockTheme as any);
    vi.mocked(useTranslation).mockReturnValue({ t: mockT } as any);

    render(<SearchButton onClick={mockOnClick} />);

    expect(screen.getByRole('button')).toBeInTheDocument();
    expect(mockT).toHaveBeenCalledWith('common.label.search');
  });

  it('should display SearchRoundedIcon within the button', () => {
    const mockOnClick = vi.fn();
    const mockTheme = { palette: { primary: { main: '#000' } } };
    const mockT = vi.fn((key) => key);

    vi.mocked(useTheme).mockReturnValue(mockTheme as any);
    vi.mocked(useTranslation).mockReturnValue({ t: mockT } as any);

    render(<SearchButton onClick={mockOnClick} />);

    const button = screen.getByRole('button');
    const icon = button.querySelector('[data-testid="SearchRoundedIcon"]');

    expect(icon).toBeInTheDocument();
  });

  it('should apply common button styles from getCommonButtonStyles with theme', () => {
    const mockOnClick = vi.fn();
    const mockTheme = { palette: { primary: { main: '#000' } } };
    const mockT = vi.fn((key) => key);
    const mockStyles = { padding: '10px', margin: '5px' };

    vi.mocked(useTheme).mockReturnValue(mockTheme as any);
    vi.mocked(useTranslation).mockReturnValue({ t: mockT } as any);

    const getCommonButtonStylesMock = vi.fn().mockReturnValue(mockStyles);
    vi.doMock('../Buttons.style', () => ({
      getCommonButtonStyles: getCommonButtonStylesMock,
    }));

    render(<SearchButton onClick={mockOnClick} />);

    const button = screen.getByRole('button');

    expect(button).toBeInTheDocument();
  });

  it('should call onClick handler when button is clicked', () => {
    const mockOnClick = vi.fn();
    const mockTheme = { palette: { primary: { main: '#000' } } };
    const mockT = vi.fn((key) => key);

    vi.mocked(useTheme).mockReturnValue(mockTheme as any);
    vi.mocked(useTranslation).mockReturnValue({ t: mockT } as any);

    render(<SearchButton onClick={mockOnClick} />);

    const button = screen.getByRole('button');
    button.click();

    expect(mockOnClick).toHaveBeenCalledTimes(1);
  });

  it('should display translated label from common.label.search as button title', () => {
    const mockOnClick = vi.fn();
    const mockTheme = { palette: { primary: { main: '#000' } } };
    const mockT = vi.fn((key) => {
      if (key === 'common.label.search') {
        return 'Search';
      }
      return key;
    });

    vi.mocked(useTheme).mockReturnValue(mockTheme as any);
    vi.mocked(useTranslation).mockReturnValue({ t: mockT } as any);

    render(<SearchButton onClick={mockOnClick} />);

    const button = screen.getByRole('button');

    expect(mockT).toHaveBeenCalledWith('common.label.search');
    expect(button).toHaveAccessibleName('Search');
  });
});
