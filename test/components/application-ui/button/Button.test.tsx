import Button from '@/components/application-ui/button/Button';
import { fireEvent, render, screen } from '@testing-library/react';
import React from 'react';
import { describe, expect, it, vi } from 'vitest';

describe('Button (MUI wrapper)', () => {
  it('renders the button text', () => {
    render(<Button>Click Me</Button>);
    expect(screen.getByText('Click Me')).toBeInTheDocument();
  });

  it('applies MUI variants correctly', () => {
    const { rerender } = render(<Button variant="text">Text</Button>);
    expect(screen.getByText('Text')).toBeInTheDocument();

    rerender(<Button variant="outlined">Outlined</Button>);
    expect(screen.getByText('Outlined')).toBeInTheDocument();

    rerender(<Button variant="contained">Contained</Button>);
    expect(screen.getByText('Contained')).toBeInTheDocument();
  });

  it('renders with different colors', () => {
    const colors = [
      'primary',
      'secondary',
      'success',
      'error',
      'info',
      'warning',
      'inherit',
    ] as const;

    colors.forEach((color) => {
      render(
        <Button
          variant="contained"
          color={color}
        >
          {color}
        </Button>
      );
      expect(screen.getByText(color)).toBeInTheDocument();
    });
  });

  it('fires onClick when clicked', () => {
    const handleClick = vi.fn();
    render(<Button onClick={handleClick}>Click</Button>);

    fireEvent.click(screen.getByText('Click'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('does not fire onClick when disabled', () => {
    const handleClick = vi.fn();
    render(
      <Button
        onClick={handleClick}
        disabled
      >
        Disabled
      </Button>
    );

    fireEvent.click(screen.getByText('Disabled'));
    expect(handleClick).not.toHaveBeenCalled();
  });

  it('can render with a custom component', () => {
    // MUI Button allows `component="a"`
    render(
      <Button
        component="a"
        href="/test"
      >
        Link Button
      </Button>
    );
    const link = screen.getByText('Link Button');
    expect(link).toHaveAttribute('href', '/test');
  });
});
