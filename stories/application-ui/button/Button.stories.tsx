import Button, { type ButtonProps } from '@/components/application-ui/button/Button';
import type { Meta, StoryObj } from '@storybook/react';

const meta: Meta<ButtonProps> = {
  title: 'Application-UI/Button',
  component: Button,
  argTypes: {
    variant: {
      control: { type: 'radio' },
      options: ['primary', 'secondary'],
      description: 'Choose between Primary or Secondary style',
      defaultValue: 'primary',
    },
    children: {
      control: { type: 'text' },
      description: 'Text displayed inside the button',
      defaultValue: 'Click Me',
    },
  },
  parameters: {
    docs: {
      description: {
        component: 'A simple Button component supporting Primary and Secondary variants',
      },
    },
  },
};

export default meta;

type Story = StoryObj<ButtonProps>;

export const Primary: Story = {
  args: {
    children: 'Primary Button',
    variant: 'primary',
  },
};

export const Secondary: Story = {
  args: {
    children: 'Secondary Button',
    variant: 'secondary',
  },
};
