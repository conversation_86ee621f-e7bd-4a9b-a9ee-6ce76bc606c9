import Button from '@/components/application-ui/button/Button';
import { fireEvent, render, screen } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';

describe('Button component', () => {
  it('renders Playground args', () => {
    render(
      <Button
        variant="contained"
        color="primary"
      >
        Click me
      </Button>
    );
    expect(screen.getByText('Click me')).toBeInTheDocument();
  });

  it('renders different variants', () => {
    const { rerender } = render(<Button variant="text">Text</Button>);
    expect(screen.getByText('Text')).toBeInTheDocument();

    rerender(<Button variant="outlined">Outlined</Button>);
    expect(screen.getByText('Outlined')).toBeInTheDocument();

    rerender(<Button variant="contained">Contained</Button>);
    expect(screen.getByText('Contained')).toBeInTheDocument();
  });

  it('renders different colors', () => {
    const colors = [
      'primary',
      'secondary',
      'success',
      'error',
      'info',
      'warning',
      'inherit',
    ] as const;
    colors.forEach((color) => {
      render(
        <Button
          variant="contained"
          color={color}
        >
          {color}
        </Button>
      );
      expect(screen.getByText(color)).toBeInTheDocument();
    });
  });

  it('handles click events', () => {
    const handleClick = vi.fn();
    render(<Button onClick={handleClick}>Click</Button>);

    fireEvent.click(screen.getByText('Click'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('does not fire click when disabled', () => {
    const handleClick = vi.fn();
    render(
      <Button
        onClick={handleClick}
        disabled
      >
        Disabled
      </Button>
    );

    fireEvent.click(screen.getByText('Disabled'));
    expect(handleClick).not.toHaveBeenCalled();
  });
});
