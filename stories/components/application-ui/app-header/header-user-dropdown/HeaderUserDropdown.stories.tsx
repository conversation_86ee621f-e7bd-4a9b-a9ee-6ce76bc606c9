import { HeaderUserDropdown } from '@/components/application-ui/app-header/header-user-dropdown/HeaderUserDropdown';
import { Box, Button as MuiButton } from '@mui/material';
import type { Meta, StoryObj } from '@storybook/react';
import { useRef, useState } from 'react';

const meta = {
  title: 'Application UI/App Header/Header User Dropdown',
  component: HeaderUserDropdown,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component:
          'A dropdown menu component that displays user profile information, menu options, and a sign-out button. Typically used in the application header.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    anchorEl: {
      description: 'The DOM element used to set the position of the menu',
      control: false,
    },
    open: {
      description: 'If true, the menu is visible',
      control: 'boolean',
    },
    onClose: {
      description: 'Callback fired when the menu requests to be closed',
      action: 'closed',
    },
    anchorOrigin: {
      description: 'The point on the anchor where the menu will attach',
      control: 'object',
    },
    transformOrigin: {
      description: 'The point on the menu which will attach to the anchor',
      control: 'object',
    },
  },
  args: {
    open: false,
    onClose: () => {},
  },
} satisfies Meta<typeof HeaderUserDropdown>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Interactive wrapper component for stories
 */
const InteractiveWrapper = (args: any) => {
  const [open, setOpen] = useState(false);
  const anchorRef = useRef<HTMLButtonElement>(null);

  const handleOpen = () => setOpen(true);
  const handleClose = () => {
    setOpen(false);
    args.onClose?.();
  };

  return (
    <Box
      sx={{ minHeight: 400, display: 'flex', alignItems: 'flex-start', justifyContent: 'center' }}
    >
      <MuiButton
        ref={anchorRef}
        variant="contained"
        onClick={handleOpen}
        id="header-button"
      >
        Open User Menu
      </MuiButton>
      <HeaderUserDropdown
        {...args}
        anchorEl={anchorRef.current}
        open={open}
        onClose={handleClose}
      />
    </Box>
  );
};

/**
 * Default state of the HeaderUserDropdown component.
 * Click the button to open the dropdown menu.
 */
export const Default: Story = {
  args: {
    open: false,
  },
  render: (args) => <InteractiveWrapper {...args} />,
};

/**
 * Dropdown with custom anchor origin positioned at bottom left.
 */
export const BottomLeftOrigin: Story = {
  args: {
    open: false,
    anchorOrigin: {
      vertical: 'bottom',
      horizontal: 'left',
    },
    transformOrigin: {
      vertical: 'top',
      horizontal: 'left',
    },
  },
  render: (args) => <InteractiveWrapper {...args} />,
};

/**
 * Dropdown with custom anchor origin positioned at bottom center.
 */
export const BottomCenterOrigin: Story = {
  args: {
    open: false,
    anchorOrigin: {
      vertical: 'bottom',
      horizontal: 'center',
    },
    transformOrigin: {
      vertical: 'top',
      horizontal: 'center',
    },
  },
  render: (args) => <InteractiveWrapper {...args} />,
};

/**
 * Dropdown positioned at top right (default positioning).
 */
export const TopRightOrigin: Story = {
  args: {
    open: false,
    anchorOrigin: {
      vertical: 'top',
      horizontal: 'right',
    },
    transformOrigin: {
      vertical: 'top',
      horizontal: 'right',
    },
  },
  render: (args) => <InteractiveWrapper {...args} />,
};

/**
 * Always open state for documentation and testing purposes.
 */
export const AlwaysOpen: Story = {
  args: {
    open: true,
  },
  render: (args) => {
    const anchorRef = useRef<HTMLButtonElement>(null);

    return (
      <Box
        sx={{ minHeight: 400, display: 'flex', alignItems: 'flex-start', justifyContent: 'center' }}
      >
        <MuiButton
          ref={anchorRef}
          variant="contained"
          id="header-button"
        >
          Anchor Button
        </MuiButton>
        <HeaderUserDropdown
          {...args}
          anchorEl={anchorRef.current}
        />
      </Box>
    );
  },
  parameters: {
    docs: {
      description: {
        story: 'This story shows the dropdown in a permanently open state for easier inspection.',
      },
    },
  },
};

/**
 * Dropdown with user having no avatar.
 * The component will display initials instead.
 */
export const WithoutAvatar: Story = {
  args: {
    open: false,
  },
  render: (args) => <InteractiveWrapper {...args} />,
  parameters: {
    docs: {
      description: {
        story:
          'When the user has no avatar, the component displays initials generated from the username.',
      },
    },
  },
};

/**
 * Dropdown in RTL (Right-to-Left) layout.
 */
export const RTLLayout: Story = {
  args: {
    open: false,
  },
  render: (args) => <InteractiveWrapper {...args} />,
  parameters: {
    direction: 'rtl',
    docs: {
      description: {
        story: 'The dropdown component properly supports RTL languages and layouts.',
      },
    },
  },
};
