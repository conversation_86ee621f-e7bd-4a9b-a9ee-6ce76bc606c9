import LanguageDropdown from '@/components/application-ui/app-header/language-dropdown/LanguageDropdown';
import { AppBar, Box, Toolbar, Typography } from '@mui/material';
import type { Meta, StoryObj } from '@storybook/react';

const meta = {
  title: 'Components/application-ui/app-header/LanguageDropdown',
  component: LanguageDropdown,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component:
          'A language dropdown component that allows users to switch between available languages. Features a flag icon button that opens a menu with language options.',
      },
    },
    reactQuery: {
      devtools: false,
    },
  },
  argTypes: {
    color: {
      control: 'select',
      options: ['inherit', 'primary', 'secondary', 'success', 'error', 'info', 'warning'],
      description: 'The color of the button',
      table: {
        type: { summary: 'string' },
        defaultValue: { summary: 'inherit' },
      },
    },
    sx: {
      control: 'object',
      description: 'System prop for custom styling',
      table: {
        type: { summary: 'SxProps<Theme>' },
        defaultValue: { summary: '{}' },
      },
    },
  },
} satisfies Meta<typeof LanguageDropdown>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Default language dropdown with inherit color
 */
export const Default: Story = {
  args: {
    color: 'inherit',
  },
};

/**
 * Language dropdown in an app bar context
 */
export const InAppBar: Story = {
  args: {
    color: 'inherit',
  },
  decorators: [
    (Story) => (
      <Box sx={{ width: '100%', minWidth: 400 }}>
        <AppBar position="static">
          <Toolbar>
            <Typography
              variant="h6"
              component="div"
              sx={{ flexGrow: 1 }}
            >
              Application Header
            </Typography>
            <Story />
          </Toolbar>
        </AppBar>
      </Box>
    ),
  ],
};
