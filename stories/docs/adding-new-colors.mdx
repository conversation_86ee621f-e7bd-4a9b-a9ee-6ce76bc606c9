import { <PERSON>, Button, Chip, Paper, Typography } from '@mui/material';
import { Meta } from '@storybook/addon-docs/blocks';

<Meta title="Documentation/Theme/Adding New Colors" />

# Adding New Colors to the Theme

This guide explains how to add new custom colors to the theme that aren't part
of the `ColorPreset` type.

## Step 1: Define the color in the theme interfaces

Open `src/theme/index.ts` and add your new color to both the `Palette` and
`PaletteOptions` interfaces:

```typescript
interface Palette {
  neutral: NeutralColors;

  // ColorPreset colors
  livingCoral: ThemeColor;
  // ... other existing colors

  // Additional custom colors
  teal: ThemeColor; // <-- Add your new color here
}

interface PaletteOptions {
  neutral?: NeutralColors;

  // ColorPreset colors
  livingCoral?: ThemeColor;
  // ... other existing colors

  // Additional custom colors
  teal?: ThemeColor; // <-- Add your new color here
}
```

## Step 2: Define the color value in colors.ts

Open `src/theme/colors.ts` and add your new color to the `baseColors` array:

```typescript
const baseColors = [
  // ... existing colors
  { name: 'honeyGold', value: '#967210' },
  { name: 'teal', value: '#008080' }, // <-- Add your new color here
  // ... other existing colors
];
```

## Step 3: Add the color to the palette in create-palette.ts

Open both `src/theme/light/create-palette.ts` and
`src/theme/dark/create-palette.ts` and add your new color:

```typescript
// Extract custom colors
const livingCoral = lightTheme.livingCoral;
// ... other existing colors
const teal = lightTheme.teal;  // <-- Add your new color here

return {
  // ... other palette properties

  // Custom colors
  livingCoral,
  // ... other existing colors
  teal,  // <-- Add your new color here
};
```

## Step 4: Add the color to PaletteColorKey

Add your new color to the `PaletteColorKey` type in `src/theme/colors.ts`. This
is the central place where all available color keys are defined:

```typescript
export type PaletteColorKey =
  | 'darkViolet'
  | 'emerald'
  | 'error'
  | 'greenery'
  | 'honeyGold'
  | 'info'
  | 'livingCoral'
  | 'monacoBlue'
  | 'neutral'
  | 'primary'
  | 'radiantOrchid'
  | 'roseQuartz'
  | 'royalBlue'
  | 'secondary'
  | 'success'
  | 'tangerineTango'
  | 'teal' // <-- Add your new color here
  | 'ultraViolet'
  | 'warning';
```

## Step 5: Extend MUI component types efficiently

To use custom colors with MUI components, you need to extend their type
definitions. Create or update a declaration file (e.g., `src/types/mui.d.ts`)
using a DRY approach:

```typescript
import '@mui/material/styles';
import '@mui/material/Button';
import '@mui/material/Chip';
// ... other MUI imports
import { type PaletteColorKey } from '@/theme/colors';

/**
 * Extend the Material UI theme to include custom colors
 */
declare module '@mui/material/styles' {
  interface Palette {
    // Custom colors are already defined in src/theme/index.ts
  }

  interface PaletteOptions {
    // Custom colors are already defined in src/theme/index.ts
  }
}

/**
 * Create a mapped type that converts all custom PaletteColorKey values to 'true'
 * This excludes the standard MUI colors which are already included
 */
type CustomColorMap = {
  [K in Exclude<
    PaletteColorKey,
    'primary' | 'secondary' | 'success' | 'error' | 'warning' | 'info'
  >]: true;
};

/**
 * Extend various MUI components to include custom colors
 */

// Button
declare module '@mui/material/Button' {
  interface ButtonPropsColorOverrides extends CustomColorMap {}
}

// Chip
declare module '@mui/material/Chip' {
  interface ChipPropsColorOverrides extends CustomColorMap {}
}

// Add more components as needed
```

With this approach, when you add a new color to `PaletteColorKey`, it will
automatically be available in all MUI components without any additional code.

## Step 6: Update component props (if needed)

If you've extended the MUI component types correctly, you can use the standard
props without any modifications. For example, for a custom Button component:

```tsx
// Before extending ButtonPropsColorOverrides
interface MyButtonProps extends Omit<ButtonProps, 'color'> {
  color?: CustomColorType; // Had to use a custom type
}

// After extending ButtonPropsColorOverrides
interface MyButtonProps extends ButtonProps {
  // No need to redefine color prop - it already includes your custom colors!
}
```

For styled components that use theme colors, you need to handle special color
values like 'inherit' that aren't in the palette:

```tsx
const StyledComponent = styled(Component)(({ theme, color }) => {
  // Handle special cases like 'inherit' which aren't in the palette
  let computedColor: string;

  if (!color || color === 'inherit') {
    // Use primary color for 'inherit' or undefined
    computedColor = theme.palette.primary.main;
  } else {
    // For all other colors, access the palette
    // We need to use type assertion to handle the complex palette type
    const paletteColor = theme.palette[
      color as keyof typeof theme.palette
    ] as any;
    computedColor = paletteColor?.main || theme.palette.primary.main;
  }

  return {
    backgroundColor: computedColor,
    // other styles
  };
});
```

## Step 7: Use your new color

Now you can use your new color anywhere in your components:

```tsx
<Button
  color="teal"
  variant="contained"
>
  Teal Button
</Button>
```

Or access it directly from the theme:

```tsx
<Box sx={{ backgroundColor: theme.palette.teal.main }}>Teal background</Box>
```

## Troubleshooting

If you encounter TypeScript errors when using custom colors, you may need to use
type assertions:

```tsx
// When accessing theme.palette
const color = (theme.palette as any)[customColor].main;

// When passing a color prop
<Component color={customColor as any} />;
```

## Best Practices

1. **Consistency**: Choose color names that are descriptive and consistent with
   your design system
2. **Accessibility**: Ensure your colors meet accessibility standards for
   contrast
3. **Reusability**: Define colors at the theme level rather than using inline
   hex values
4. **Type Safety**: Use the type system to catch errors at compile time rather
   than runtime
5. **Documentation**: Document your color system for other developers
