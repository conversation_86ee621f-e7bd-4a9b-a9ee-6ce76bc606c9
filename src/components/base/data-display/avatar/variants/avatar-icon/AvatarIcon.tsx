import CheckCircleOutlinedIcon from '@mui/icons-material/CheckCircleOutlined';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import WarningAmberOutlinedIcon from '@mui/icons-material/WarningAmberOutlined';
import React from 'react';
import { type AvatarIconProps } from '../../Avatar.type';
import { AvatarState } from '../avatar-state/AvatarState';

/**
 * A standardized avatar component that displays an icon with consistent styling.
 *
 * @example
 * ```tsx
 * <AvatarIcon
 *   icon={<WarningTwoToneIcon />}
 *   state="warning"
 *   size={64}
 * />
 * ```
 */
export const AvatarIcon = ({
  icon,
  state = 'primary',
  isSoft = true,
  size = 40,
  sx,
  ...otherProps
}: AvatarIconProps) => {
  // AvatarState will handle theme mode internally through the getStateStyles function
  // Determine the appropriate font size based on avatar size
  const getFontSize = () => {
    if (size >= 64) return 'large';
    if (size >= 40) return 'medium';
    return 'small';
  };

  // Clone the icon element to apply fontSize
  const iconElement = icon
    ? React.cloneElement(icon, {
        fontSize: getFontSize(),
      })
    : null;

  return (
    <AvatarState
      state={state}
      isSoft={isSoft}
      sx={{
        width: size,
        height: size,
        ...sx,
      }}
      {...otherProps}
    >
      {iconElement}
    </AvatarState>
  );
};

export default AvatarIcon;

/**
 * Pre-configured avatar components for common use cases
 */

/**
 * Success avatar with CheckCircle icon
 */
export const SuccessAvatar = (props: Omit<AvatarIconProps, 'icon' | 'state'>) => (
  <AvatarIcon
    icon={<CheckCircleOutlinedIcon />}
    state="success"
    data-testid="success-avatar"
    {...props}
  />
);

/**
 * Error avatar with Error icon
 */
export const ErrorAvatar = (props: Omit<AvatarIconProps, 'icon' | 'state'>) => (
  <AvatarIcon
    icon={<ErrorOutlineIcon />}
    state="error"
    data-testid="error-avatar"
    {...props}
  />
);

/**
 * Warning avatar with Warning icon
 */
export const WarningAvatar = (props: Omit<AvatarIconProps, 'icon' | 'state'>) => (
  <AvatarIcon
    icon={<WarningAmberOutlinedIcon />}
    state="warning"
    data-testid="warning-avatar"
    {...props}
  />
);

/**
 * Info avatar with Info icon
 */
export const InfoAvatar = (props: Omit<AvatarIconProps, 'icon' | 'state'>) => (
  <AvatarIcon
    icon={<InfoOutlinedIcon />}
    state="info"
    data-testid="info-avatar"
    {...props}
  />
);
