'use client';

import {
  isServer,
  QueryClient,
  QueryClientProvider,
  type QueryClientConfig,
} from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';

/**
 * Creates a new QueryClient instance with optional configuration.
 *
 * @param defaultOptions - Optional configuration options for the QueryClient
 * @returns A new QueryClient instance
 */
const makeQueryClient = (defaultOptions?: QueryClientConfig) => {
  return new QueryClient(defaultOptions);
};

let browserQueryClient: QueryClient | undefined;

/**
 * Browser-side QueryClient singleton instance.
 * Maintains query cache across component re-renders on the client side.
 *
 * Server always make a new query client
 */
const getQueryClient = () => {
  if (isServer) {
    return makeQueryClient({
      defaultOptions: {
        queries: {
          // SSR request stale for 10 seconds
          staleTime: 10 * 1000,
        },
      },
    });
  } else {
    browserQueryClient ??= makeQueryClient();
    return browserQueryClient;
  }
};

/**
 * TanStack Query provider component that wraps the application with QueryClient.
 * Provides query caching, background refetching, and development tools.
 *
 * @param children - React children to be wrapped with the query provider
 * @param showDevtools - Whether to show ReactQueryDevtools (default: true)
 * @returns JSX element with QueryClientProvider and ReactQueryDevtools
 */
export const Provider = ({
  children,
  showDevtools = true,
}: {
  children: React.ReactNode;
  showDevtools?: boolean;
}) => {
  const queryClient = getQueryClient();

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      {showDevtools && <ReactQueryDevtools initialIsOpen={false} />}
    </QueryClientProvider>
  );
};
