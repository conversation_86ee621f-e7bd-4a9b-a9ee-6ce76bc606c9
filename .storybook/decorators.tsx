import { AppStoreProvider } from '@/contexts/app';
import i18n from '@/providers/i18n/i18n';
import { Provider as QueryProvider } from '@/providers/tanstack/query/Provider';
import {
  DataGridPremium,
  Toolbar,
  type GridLocaleText,
  type GridPremiumSlotsComponent,
} from '@mui/x-data-grid-premium';
import { AdapterDateFns } from '@mui/x-date-pickers-pro/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers-pro/LocalizationProvider';
import type { StoryContext } from '@storybook/nextjs-vite';
import { NavigationGuardProvider } from 'next-navigation-guard';
import React from 'react';
import { I18nextProvider, useTranslation } from 'react-i18next';
import { useLocaleText } from '../src/components/base/data-display/datagrid/DataGrid.util';
import { AuthProvider } from '../src/contexts/auth/auth.context';
import { getDateFnsLocale, getMuiLocaleText } from '../src/theme/shared/locales';

/**
 * Decorator that wraps components with AuthProvider
 * This is necessary for components that use the useAuth hook
 */
export const withAuthProvider = (Story: React.ComponentType) => (
  <AuthProvider>
    <Story />
  </AuthProvider>
);

/**
 * Decorator that provides internationalization support for Storybook stories
 */
export const withI18next = (Story: React.ComponentType, context: StoryContext) => {
  const I18nWrapper = () => {
    const { locale } = context.globals;

    // When the locale global changes, set the new locale in i18n
    React.useEffect(() => {
      i18n.changeLanguage(locale);
    }, [locale]);

    return (
      <I18nextProvider i18n={i18n}>
        <Story />
      </I18nextProvider>
    );
  };

  return <I18nWrapper />;
};

/**
 * Mock implementation of GuestGuard for Storybook
 * This simply renders children without any authentication checks
 */
export const MockGuestGuard = ({ children }: { children: React.ReactNode }) => {
  return <>{children}</>;
};

/**
 * Mock implementation of useRouter for Storybook
 */
export const mockRouter = {
  push: () => {},
  replace: () => {},
  refresh: () => {},
  back: () => {},
  forward: () => {},
  prefetch: () => Promise.resolve(),
  pathname: '/',
  query: {},
};

/**
 * Decorator that wraps a component in a DataGridPremium with toolbar
 *
 * @param Story The story component to wrap
 * @returns The decorated story component
 */
export const withDataGrid = (Story: React.ComponentType) => {
  const CustomToolbarComponent = () => (
    <Toolbar>
      <Story />
    </Toolbar>
  );

  const DataGridWrapper = () => {
    const defaultSlotProps: Partial<GridPremiumSlotsComponent> = {
      toolbar: CustomToolbarComponent,
    };

    const { i18n, t } = useTranslation();
    const localeText: Partial<GridLocaleText> = useLocaleText(i18n.language as Language, t);

    return (
      <DataGridPremium
        columns={[
          { field: 'id', headerName: 'ID' },
          { field: 'username', headerName: 'Username' },
        ]}
        rows={[
          { id: 1, username: 'John Doe' },
          { id: 2, username: 'Jane Doe' },
          { id: 3, username: 'Richard Smith' },
        ]}
        disableRowSelectionOnClick
        disableAggregation
        showToolbar
        disablePivoting
        localeText={localeText}
        slots={defaultSlotProps}
      />
    );
  };

  return <DataGridWrapper />;
};

/**
 * Decorator that wraps stories with MUI's LocalizationProvider
 * Needed for all @mui/x-date-pickers components
 */
export const withLocalizationProvider = (Story: React.ComponentType, context: StoryContext) => {
  const { locale } = context.globals;
  return (
    <LocalizationProvider
      key={locale}
      dateAdapter={AdapterDateFns}
      adapterLocale={getDateFnsLocale(locale)}
      localeText={getMuiLocaleText(locale)}
    >
      <Story />
    </LocalizationProvider>
  );
};

/**
 * Decorator that wraps components with NavigationGuardProvider
 */
export const withNavigationGuardProvider = (Story: React.ComponentType) => {
  // Workaround `Cannot read properties of null (reading '__next_navigation_guard_stack_index')` error
  window.history.replaceState(
    { ...window.history.state, __next_navigation_guard_stack_index: 0 },
    '',
    window.location.href
  );

  return (
    <NavigationGuardProvider>
      <Story />
    </NavigationGuardProvider>
  );
};

/**
 * Decorator that wraps components with global AppStoreProvider
 */
export const withAppStoreProvider = (Story: React.ComponentType) => (
  <AppStoreProvider>
    <Story />
  </AppStoreProvider>
);

/**
 * Decorator that wraps components with TanStack QueryProvider
 */
export const withQueryProvider = (Story: React.ComponentType, context: StoryContext) => {
  // Check if devtools should be disabled
  const showDevtools = context.parameters?.reactQuery?.devtools !== false;

  return (
    <QueryProvider showDevtools={showDevtools}>
      <Story />
    </QueryProvider>
  );
};
