import { CssBaseline, ThemeProvider } from '@mui/material';
import { withThemeFromJSXProvider } from '@storybook/addon-themes';
import { darkTheme, lightTheme } from '../config/theme';

export const decorators = [
  withThemeFromJSXProvider({
    Provider: ThemeProvider,
    GlobalStyles: CssBaseline,
    themes: {
      light: lightTheme,
      dark: darkTheme,
    },
    defaultTheme: 'light',
  }),
];

export const parameters = {};
