{"name": "wlsup", "version": "0.0.1", "private": true, "scripts": {"build-storybook": "storybook build", "build": "next build", "dev": "PORT=3003 next dev --turbopack", "docker:build": "docker compose build --no-cache", "docker:clean": "docker compose down --volumes --remove-orphans", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "docker:rebuild": "docker compose down && docker compose build --no-cache && docker compose up -d", "docker:up": "docker compose up -d", "format:check": "prettier --config ./config/prettier.config.mjs --check \"**/*.{js,jsx,ts,tsx,mdx}\"", "format:write": "prettier --config ./config/prettier.config.mjs --write \"**/*.{js,jsx,ts,tsx,mdx}\"", "lint-fix": "eslint --config ./config/eslint.config.mjs --fix", "lint": "eslint --config ./config/eslint.config.mjs . --max-warnings=10 && echo '✔ Linting complete. No issues found!'", "prepare": "husky", "sonar:local": "./scripts/run-sonar-local.sh", "start": "PORT=3003 next start", "storybook:clean": "rm -rf node_modules/.cache/storybook && rm -rf .next/cache && rm -rf .storybook/cache && rm -rf node_modules/.vite", "storybook:fresh": "npm run storybook:clean && npm run storybook", "storybook": "storybook dev -p 6006", "test:components": "vitest --config ./config/vitest.config.mts test/components --run", "test:coverage:storybook": "vitest --config ./config/vitest.storybook.config.mts --coverage", "test:coverage": "vitest --config ./config/vitest.config.mts --coverage --run", "test:storybook": "vitest --config ./config/vitest.storybook.config.mts --run", "test:watch": "vitest --config ./config/vitest.config.mts --typecheck", "test": "vitest --config ./config/vitest.config.mts --typecheck --run", "type-check": "npx tsc --noEmit", "validate": "npm-run-all --parallel lint type-check test"}, "dependencies": {"@emotion/cache": "^11.14.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@mui/icons-material": "^7.3.2", "@mui/material": "^7.3.2", "@mui/material-nextjs": "^7.3.2", "@sentry/nextjs": "^10.11.0", "axios": "^1.10.0", "next": "15.5.2", "react": "19.1.0", "react-dom": "19.1.0"}, "devDependencies": {"@chromatic-com/storybook": "^4.1.1", "@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@eslint/compat": "^1.2.7", "@eslint/eslintrc": "^3.3.0", "@eslint/js": "^9.22.0", "@ianvs/prettier-plugin-sort-imports": "4.1.1", "@storybook/addon-a11y": "^9.1.5", "@storybook/addon-docs": "^9.1.5", "@storybook/addon-themes": "^9.1.5", "@storybook/addon-vitest": "^9.1.5", "@storybook/nextjs-vite": "^9.1.5", "@storybook/react": "^9.1.5", "@tanstack/eslint-plugin-query": "^5.81.2", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@vitejs/plugin-react": "^5.0.2", "@vitest/browser": "^3.2.4", "@vitest/coverage-v8": "^3.2.4", "c8": "^10.1.3", "dotenv": "^17.2.1", "eslint": "^9", "eslint-config-next": "15.5.2", "eslint-config-prettier": "^10.1.1", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-sonarjs": "^3.0.2", "eslint-plugin-storybook": "^9.1.2", "husky": "^9.1.7", "jsdom": "^26.1.0", "lint-staged": "^15.4.3", "msw-storybook-addon": "^2.0.5", "npm-run-all": "^4.1.5", "playwright": "^1.55.0", "prettier": "^3.5.3", "storybook": "^9.1.5", "typescript": "^5", "vitest": "^3.2.4"}, "lint-staged": {"**/*.{js,jsx,ts,tsx}": ["prettier --config ./config/prettier.config.mjs --write", "eslint --config ./config/eslint.config.mjs --fix"], "**/*.{json,css,md,mdx}": ["prettier --config ./config/prettier.config.mjs --write"]}, "eslintConfig": {"extends": ["plugin:storybook/recommended"]}}