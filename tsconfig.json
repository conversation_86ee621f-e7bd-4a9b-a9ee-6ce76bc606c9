{
	"compilerOptions": {
		"baseUrl": ".",
		"allowJs": true,
		"allowSyntheticDefaultImports": true,
		"esModuleInterop": true,
		"forceConsistentCasingInFileNames": true,
		"incremental": true,
		"isolatedModules": true,
		"jsx": "preserve",
		"lib": ["dom", "dom.iterable", "esnext"],
		"module": "esnext",
		"moduleResolution": "bundler",
		"noEmit": true,

		// Additional type checking
		"noImplicitAny": true,
		"noImplicitReturns": true,
		"noImplicitThis": true,
		"noUncheckedIndexedAccess": true,
		"noUnusedLocals": true,
		"noUnusedParameters": true,
		"resolveJsonModule": true,
		"skipLibCheck": true,
		"strict": true,
		"strictBindCallApply": true,
		"strictFunctionTypes": true,
		"strictNullChecks": true,
		"strictPropertyInitialization": true,
		"target": "ES2017",
		"useUnknownInCatchVariables": true,
		"verbatimModuleSyntax": true,

		"paths": {
			"@/*": ["./src/*"]
		},
		"types": ["vitest/globals"],
		"plugins": [
			{
				"name": "next"
			}
		]
	},
	"include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
	"exclude": ["node_modules"]
}
